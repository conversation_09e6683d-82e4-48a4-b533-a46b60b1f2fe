import '../entities/auth_user.dart';
import '../repositories/auth_repository.dart';

/// Use case for user logout operations
///
/// This use case encapsulates the business logic for user logout operations.
/// It follows the single responsibility principle by handling only logout-related
/// business rules and delegating data operations to the repository.
///
/// The use case provides both regular logout (current session) and global logout
/// (all sessions) functionality.
class LogoutUseCase {
  final AuthRepository _authRepository;

  const LogoutUseCase(this._authRepository);

  /// Executes the logout use case for the current session
  ///
  /// This method performs the following operations:
  /// 1. Logs out the user from the current session
  /// 2. Invalidates the current session on the server
  /// 3. Clears local authentication tokens and cached data
  ///
  /// This operation is designed to always succeed locally even if the server
  /// logout fails, ensuring the user can always log out from the app.
  ///
  /// Business Rules:
  /// - Local logout always succeeds regardless of network connectivity
  /// - Server logout is attempted but failures don't prevent local logout
  /// - All local authentication data is cleared
  ///
  /// Does not throw exceptions - any errors are handled internally
  /// to ensure logout always completes successfully.
  Future<void> call() async {
    try {
      await _authRepository.logout();
    } catch (e) {
      // Log the error but don't throw it - logout should always succeed locally
      // In a real implementation, you might want to log this error to a logging service
      // For now, we silently handle the error to ensure logout completes
    }
  }

  /// Executes the global logout use case for all sessions
  ///
  /// This method performs the following operations:
  /// 1. Logs out the user from all active sessions globally
  /// 2. Invalidates all user sessions on the server
  /// 3. Clears local authentication tokens and cached data
  ///
  /// This operation requires network connectivity to invalidate server sessions.
  /// If the operation fails, the user remains logged in on other devices.
  ///
  /// Business Rules:
  /// - Requires active network connection
  /// - Invalidates sessions on all devices
  /// - Local logout always succeeds regardless of server response
  ///
  /// Throws:
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  ///
  /// Note: Even if server logout fails, local logout will still be performed
  /// to ensure the user is logged out from the current device.
  Future<void> callGlobal() async {
    try {
      await _authRepository.globalLogout();
    } catch (e) {
      // Ensure local logout happens even if global logout fails
      await call();
      // Re-throw the exception to inform the caller about the global logout failure
      rethrow;
    }
  }
}

/// Use case for checking authentication status
///
/// This use case encapsulates the business logic for checking if a user
/// is currently authenticated. It provides a clean interface for other
/// parts of the application to verify authentication status.
class CheckAuthenticationUseCase {
  final AuthRepository _authRepository;

  const CheckAuthenticationUseCase(this._authRepository);

  /// Checks if the user is currently authenticated
  ///
  /// This method performs the following checks:
  /// 1. Verifies presence of valid authentication tokens
  /// 2. Checks token expiration status
  /// 3. Validates session status
  ///
  /// Returns true if the user is authenticated and the session is valid,
  /// false otherwise.
  ///
  /// Business Rules:
  /// - Returns false for any error conditions
  /// - Does not throw exceptions
  /// - Performs comprehensive authentication validation
  ///
  /// This method is safe to call frequently as it's designed to be lightweight
  /// and not perform expensive operations.
  Future<bool> call() async {
    try {
      return await _authRepository.isAuthenticated();
    } catch (e) {
      // Return false for any error conditions
      return false;
    }
  }
}

/// Use case for retrieving the current user
///
/// This use case encapsulates the business logic for retrieving the currently
/// authenticated user's information. It provides a clean interface for accessing
/// user data throughout the application.
class GetCurrentUserUseCase {
  final AuthRepository _authRepository;

  const GetCurrentUserUseCase(this._authRepository);

  /// Retrieves the currently authenticated user
  ///
  /// This method performs the following operations:
  /// 1. Checks if a user is currently authenticated
  /// 2. Retrieves the user data from secure storage or cache
  /// 3. Returns the user information
  ///
  /// Returns the authenticated user if available, null otherwise.
  ///
  /// Business Rules:
  /// - Returns null if no user is authenticated
  /// - Returns null for any error conditions
  /// - Does not throw exceptions
  /// - May return cached user data for performance
  ///
  /// This method is safe to call frequently and is designed to be lightweight.
  Future<AuthUser?> call() async {
    try {
      return await _authRepository.getCurrentUser();
    } catch (e) {
      // Return null for any error conditions
      return null;
    }
  }
}
