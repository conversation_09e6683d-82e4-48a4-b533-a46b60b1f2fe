import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';

/// Use case for user authentication (login)
/// 
/// This use case encapsulates the business logic for user login operations.
/// It follows the single responsibility principle by handling only login-related
/// business rules and delegating data operations to the repository.
/// 
/// The use case validates input parameters and coordinates with the repository
/// to perform the actual authentication operation.
class LoginUseCase {
  final AuthRepository _authRepository;

  const LoginUseCase(this._authRepository);

  /// Executes the login use case
  /// 
  /// This method performs the following operations:
  /// 1. Validates the input parameters
  /// 2. Delegates to the repository for authentication
  /// 3. Returns the authentication result
  /// 
  /// Parameters:
  /// - [email]: User's email address (must be valid email format)
  /// - [password]: User's password (must not be empty)
  /// 
  /// Returns [AuthResult] containing:
  /// - Success with user data and tokens if authentication succeeds
  /// - Two-factor required result if 2FA is needed
  /// - Failure result with error message if authentication fails or validation fails
  /// 
  /// Business Rules:
  /// - Email must be in valid email format
  /// - Password must not be empty or whitespace only
  /// - Email is case-insensitive (converted to lowercase)
  /// 
  /// Throws:
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  /// - AuthException for authentication-specific errors
  Future<AuthResult> call(String email, String password) async {
    // Validate input parameters
    final validationResult = _validateLoginParameters(email, password);
    if (validationResult != null) {
      return validationResult;
    }

    // Normalize email to lowercase for consistency
    final normalizedEmail = email.trim().toLowerCase();
    final trimmedPassword = password.trim();

    // Delegate to repository for authentication
    return await _authRepository.login(normalizedEmail, trimmedPassword);
  }

  /// Validates login parameters according to business rules
  /// 
  /// Returns null if validation passes, or an AuthResult.failure if validation fails
  AuthResult? _validateLoginParameters(String email, String password) {
    // Check if email is provided
    if (email.trim().isEmpty) {
      return AuthResult.failure(error: 'Email address is required');
    }

    // Check if password is provided
    if (password.trim().isEmpty) {
      return AuthResult.failure(error: 'Password is required');
    }

    // Validate email format using a simple regex
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    if (!emailRegex.hasMatch(email.trim())) {
      return AuthResult.failure(error: 'Please enter a valid email address');
    }

    // Check password minimum length (business rule)
    if (password.trim().length < 6) {
      return AuthResult.failure(error: 'Password must be at least 6 characters long');
    }

    return null; // Validation passed
  }
}

/// Use case for biometric authentication
/// 
/// This use case encapsulates the business logic for biometric login operations.
/// It handles the specific requirements and validations for biometric authentication.
class BiometricLoginUseCase {
  final AuthRepository _authRepository;

  const BiometricLoginUseCase(this._authRepository);

  /// Executes the biometric login use case
  /// 
  /// This method performs the following operations:
  /// 1. Validates the email parameter
  /// 2. Delegates to the repository for biometric authentication
  /// 3. Returns the authentication result
  /// 
  /// Parameters:
  /// - [email]: User's email address (must be valid email format)
  /// 
  /// Returns [AuthResult] containing:
  /// - Success with user data and tokens if biometric authentication succeeds
  /// - Failure result with error message if biometric authentication fails
  /// 
  /// Business Rules:
  /// - Email must be in valid email format
  /// - User must have previously set up biometric authentication
  /// - Email is case-insensitive (converted to lowercase)
  /// 
  /// Throws:
  /// - BiometricException for biometric-specific errors
  /// - NetworkException for connectivity issues
  /// - AuthException for authentication-specific errors
  Future<AuthResult> call(String email) async {
    // Validate email parameter
    final validationResult = _validateEmail(email);
    if (validationResult != null) {
      return validationResult;
    }

    // Normalize email to lowercase for consistency
    final normalizedEmail = email.trim().toLowerCase();

    // Delegate to repository for biometric authentication
    return await _authRepository.loginWithBiometric(normalizedEmail);
  }

  /// Validates email parameter according to business rules
  /// 
  /// Returns null if validation passes, or an AuthResult.failure if validation fails
  AuthResult? _validateEmail(String email) {
    // Check if email is provided
    if (email.trim().isEmpty) {
      return AuthResult.failure(error: 'Email address is required for biometric login');
    }

    // Validate email format using a simple regex
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    if (!emailRegex.hasMatch(email.trim())) {
      return AuthResult.failure(error: 'Please enter a valid email address');
    }

    return null; // Validation passed
  }
}
