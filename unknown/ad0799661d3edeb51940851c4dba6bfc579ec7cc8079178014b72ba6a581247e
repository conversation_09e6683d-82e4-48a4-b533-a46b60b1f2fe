import '../entities/auth_result.dart';
import '../entities/auth_user.dart';

/// Abstract repository interface for authentication operations
/// 
/// This repository defines the contract for authentication-related data operations
/// following clean architecture principles. It serves as the boundary between
/// the domain layer and the data layer, ensuring dependency inversion.
/// 
/// All implementations of this repository should handle:
/// - Network connectivity issues
/// - Token management and refresh
/// - Secure storage of credentials
/// - Error handling and mapping to domain failures
abstract class AuthRepository {
  /// Authenticates a user with email and password
  /// 
  /// Returns [AuthResult] containing:
  /// - Success with user data and tokens if authentication succeeds
  /// - Two-factor required result if 2FA is needed
  /// - Failure result with error message if authentication fails
  /// 
  /// Throws:
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  /// - AuthException for authentication-specific errors
  Future<AuthResult> login(String email, String password);

  /// Authenticates a user using biometric authentication
  /// 
  /// This method assumes the user has previously set up biometric authentication
  /// and stored credentials securely. The email parameter is used to identify
  /// the user account for biometric authentication.
  /// 
  /// Returns [AuthResult] containing:
  /// - Success with user data and tokens if biometric authentication succeeds
  /// - Failure result with error message if biometric authentication fails
  /// 
  /// Throws:
  /// - BiometricException for biometric-specific errors
  /// - NetworkException for connectivity issues
  /// - AuthException for authentication-specific errors
  Future<AuthResult> loginWithBiometric(String email);

  /// Logs out the current user from the current session
  /// 
  /// This method:
  /// - Invalidates the current session on the server
  /// - Clears local authentication tokens
  /// - Clears any cached user data
  /// 
  /// Does not throw exceptions - failures are logged but not propagated
  /// to ensure the user can always log out locally even if server logout fails.
  Future<void> logout();

  /// Logs out the user from all active sessions globally
  /// 
  /// This method:
  /// - Invalidates all user sessions on the server
  /// - Clears local authentication tokens
  /// - Clears any cached user data
  /// 
  /// Throws:
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  Future<void> globalLogout();

  /// Verifies a two-factor authentication code
  /// 
  /// This method completes the two-factor authentication process by verifying
  /// the provided code against the reference code from the initial login attempt.
  /// 
  /// Parameters:
  /// - [refCode]: Reference code received from the initial login attempt
  /// - [code]: Two-factor authentication code provided by the user
  /// 
  /// Returns [AuthResult] containing:
  /// - Success with user data and tokens if 2FA verification succeeds
  /// - Failure result with error message if 2FA verification fails
  /// 
  /// Throws:
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  /// - AuthException for authentication-specific errors
  Future<AuthResult> verifyTwoFactor(String refCode, String code);

  /// Refreshes the current authentication token
  /// 
  /// This method uses the stored refresh token to obtain a new access token
  /// when the current token expires or is about to expire.
  /// 
  /// Returns the new access token as a String.
  /// 
  /// Throws:
  /// - TokenExpiredException if the refresh token has expired
  /// - NetworkException for connectivity issues
  /// - ServerException for server-side errors
  /// - AuthException if token refresh fails
  Future<String> refreshToken();

  /// Checks if the user is currently authenticated
  /// 
  /// This method verifies:
  /// - Presence of valid authentication tokens
  /// - Token expiration status
  /// - Session validity
  /// 
  /// Returns true if the user is authenticated and the session is valid,
  /// false otherwise.
  /// 
  /// This method should not throw exceptions and should return false
  /// for any error conditions.
  Future<bool> isAuthenticated();

  /// Retrieves the currently authenticated user
  /// 
  /// Returns the [AuthUser] if a user is currently authenticated,
  /// null if no user is authenticated or if the session is invalid.
  /// 
  /// This method should not throw exceptions and should return null
  /// for any error conditions.
  Future<AuthUser?> getCurrentUser();
}
