import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:local_auth/local_auth.dart';

import '../../domain/entities/auth_result.dart';
import '../../domain/entities/auth_user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../view_model/loginPostRequests.dart';

/// Temporary implementation of AuthRepository
///
/// This is a bridge implementation that connects the new domain layer
/// to the existing LoginPostRequests system. This implementation will be
/// replaced with a proper data layer implementation in Task 4.
///
/// This implementation:
/// - Delegates authentication operations to LoginPostRequests
/// - Converts existing data structures to domain entities
/// - Provides the interface required by the domain layer use cases
///
/// Note: This is a temporary solution to enable dependency injection
/// while maintaining compatibility with the existing authentication system.
class AuthRepositoryTempImpl implements AuthRepository {
  final FlutterSecureStorage secureStorage;
  final http.Client httpClient;
  final LocalAuthentication localAuth;

  const AuthRepositoryTempImpl({
    required this.secureStorage,
    required this.httpClient,
    required this.localAuth,
  });

  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      // Use existing LoginPostRequests for actual authentication
      // LoginPostRequests.login returns String? (null for success, refCode for 2FA)
      final result = await LoginPostRequests.login(email, password);

      if (result == null) {
        // Login successful, get user info
        final userInfo = await LoginPostRequests.tokenCheck();
        final user = _convertToAuthUser(Map<String, dynamic>.from(userInfo));

        // Get tokens from secure storage
        final accessToken = await secureStorage.read(key: 'access_token');
        final refreshToken = await secureStorage.read(key: 'refresh_token');

        return AuthResult.success(
          user: user,
          accessToken: accessToken,
          refreshToken: refreshToken,
        );
      } else {
        // Two-factor authentication required
        return AuthResult.twoFactorRequired(
          twoFactorRefCode: result,
        );
      }
    } catch (e) {
      return AuthResult.failure(
        error: e.toString().replaceAll('CustomException: ', ''),
      );
    }
  }

  @override
  Future<AuthResult> loginWithBiometric(String email) async {
    try {
      // Check if biometric is available and enabled
      final isAvailable = await localAuth.isDeviceSupported();
      if (!isAvailable) {
        return AuthResult.failure(
            error: 'Biometric authentication not available');
      }

      // Get stored credentials for biometric login
      final storedEmail = await secureStorage.read(key: 'email');
      final storedPassword = await secureStorage.read(key: 'password');

      if (storedEmail != email || storedPassword == null) {
        return AuthResult.failure(
            error: 'No biometric credentials stored for this email');
      }

      // Authenticate with biometrics
      final authenticated = await localAuth.authenticate(
        localizedReason: 'Please authenticate to login',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!authenticated) {
        return AuthResult.failure(error: 'Biometric authentication failed');
      }

      // Use stored credentials to login
      return await login(email, storedPassword);
    } catch (e) {
      return AuthResult.failure(
        error: 'Biometric authentication failed: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      await LoginPostRequests.logout();
    } catch (e) {
      // Log the error but don't throw - logout should always succeed locally
      print('Logout error: $e');
    }
  }

  @override
  Future<void> globalLogout() async {
    try {
      await LoginPostRequests.globalLogout();
    } catch (e) {
      // Ensure local logout happens even if global logout fails
      await logout();
      rethrow;
    }
  }

  @override
  Future<AuthResult> verifyTwoFactor(String refCode, String code) async {
    try {
      // Use existing sendTwoFactorCode method
      await LoginPostRequests.sendTwoFactorCode(refCode, code);

      // If successful, get user info
      final userInfo = await LoginPostRequests.tokenCheck();
      final user = _convertToAuthUser(Map<String, dynamic>.from(userInfo));

      // Get tokens from secure storage
      final accessToken = await secureStorage.read(key: 'access_token');
      final refreshToken = await secureStorage.read(key: 'refresh_token');

      return AuthResult.success(
        user: user,
        accessToken: accessToken,
        refreshToken: refreshToken,
      );
    } catch (e) {
      return AuthResult.failure(
        error: e.toString().replaceAll('CustomException: ', ''),
      );
    }
  }

  @override
  Future<String> refreshToken() async {
    try {
      return await LoginPostRequests.getNewAccessToken();
    } catch (e) {
      throw Exception('Token refresh failed: ${e.toString()}');
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      return LoginPostRequests.isLoggedIn;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<AuthUser?> getCurrentUser() async {
    try {
      if (!LoginPostRequests.isLoggedIn) {
        return null;
      }

      final userInfo = await LoginPostRequests.tokenCheck();
      return _convertToAuthUser(Map<String, dynamic>.from(userInfo));
    } catch (e) {
      return null;
    }
  }

  /// Converts existing user data structure to domain AuthUser entity
  ///
  /// This method adapts the existing user data format to the new
  /// domain entity structure. This conversion will be moved to proper
  /// data models in Task 4.
  AuthUser _convertToAuthUser(Map<String, dynamic> userData) {
    return AuthUser(
      id: userData['userID']?.toString() ?? userData['id']?.toString() ?? '',
      name: userData['name']?.toString() ?? '',
      email: userData['email']?.toString() ?? '',
      emailVerified: userData['emailVerified'] == true,
      phone: userData['phone']?.toString() ?? '',
      phoneVerified: userData['phoneVerified'] == true,
      lastPassChange: userData['lastPassChange']?.toString(),
      lastUpdate: userData['lastUpdate'] is int
          ? userData['lastUpdate']
          : int.tryParse(userData['lastUpdate']?.toString() ?? ''),
      multiFactor: userData['multiFactor']?.toString(),
    );
  }
}
