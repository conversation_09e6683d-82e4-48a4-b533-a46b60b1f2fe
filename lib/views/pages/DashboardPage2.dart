import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:water_metering/utils/pok.dart';
import 'package:water_metering/views/dashboards/SummaryTable.dart';
import 'package:water_metering/views/pages/DevicesPage.dart';
import 'package:water_metering/views/widgets/drawers/profile/ProfileDrawer.dart';

import '../../bloc/dashboardBloc/dashboardState.dart';
import '../../bloc/dashboardBloc/dashboardBloc.dart';
import '../../core/di/injection_container.dart';
import '../../theme/theme2.dart';
import '../../utils/alert_message.dart';
import '../../utils/loader.dart';
import '../../utils/new_loader.dart';
import '../dashboards/TrendsChartCombined.dart';
import '../widgets/containers/CustomAppBar.dart';
import '../widgets/containers/customButton.dart';
import 'BackgroundChart.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  _DashboardPageState createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  int drawerIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var fullScreens = [
      MainDashboardPage(),
      BackgroundChart(),
    ];

    return MultiProvider(
      providers: [
        BlocProvider(create: (context) => sl<DashboardBloc>()),
      ],
      child: SafeArea(
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: BlocBuilder<DashboardBloc, DashboardState>(
              buildWhen: (previous, current) {
            if ((current is DashboardPageError ||
                current is DashboardPageLoaded ||
                current is ChangeScreen)) {
              return true;
            }
            return false;
          }, builder: (context, state) {
            if (state is DashboardPageLoaded || state is ChangeScreen) {
              return GestureDetector(
                  onTap: () => FocusScope.of(context).unfocus(),
                  child: IndexedStack(
                    index: BlocProvider.of<DashboardBloc>(context).screenIndex,
                    children: fullScreens,
                  ));
            } else if (state is DashboardPageError) {
              return Scaffold(
                body: Center(
                  child: SizedBox(
                    height: 600.h,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text("ERROR IN FETCHING DATA. REFRESH LATER",
                            style: GoogleFonts.roboto(
                              color: CommonColors.blue,
                              fontSize: ThemeNotifier.medium.minSp,
                              fontWeight: FontWeight.w500,
                            )),
                        const SizedBox(height: 20),
                        CustomButton(
                          text: "REFRESH",
                          onPressed: () {
                            LoaderUtility.showLoader(
                                    context,
                                    BlocProvider.of<DashboardBloc>(context)
                                        .loadInitialData())
                                .then((s) {})
                                .catchError((e) {
                              CustomAlert.showCustomScaffoldMessenger(context,
                                  "Error in loading data", AlertType.error);
                            });
                          },
                        )
                      ],
                    ),
                  ),
                ),
              );
            }
            return const CustomLoader();
          }),
        ),
      ),
    );
  }
}

class MainDashboardPage extends StatefulWidget {
  static List<String> bottomNavTabs = [
    'trends',
    'billing',
    'activity',
    'profile',
  ];

  const MainDashboardPage({super.key});

  @override
  State<MainDashboardPage> createState() => _MainDashboardPageState();
}

class _MainDashboardPageState extends State<MainDashboardPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int drawerIndex = 0;
  List<IconData> icons = [
    Icons.trending_up_sharp,
    Icons.summarize_outlined,
    Icons.person_outline_rounded,
  ];

  List<Color> selectedColor = [
    const Color(0xFFDFAC46),
    CommonColors.green,
    CommonColors.red,
    CommonColors.blue2
  ];

  List<String> bottomNavTabIcons = [
    'trends',
    'billing',
    'activity',
    'profile',
  ];

  // List<IconData> bottomNavTabsIcons = [
  //   Icons.trending_up,
  //   Icons.summarize,
  //   Icons.person
  // ];

  @override
  void initState() {
    super.initState();
    print("YES I AM BEING HIT");
    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        MainDashboardPage.bottomNavTabs =
            (await DashboardBloc.updateBottomNavTabs(
                project: BlocProvider.of<DashboardBloc>(context)
                    .currentFilters
                    .firstOrNull))!;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    List<Widget> list = [
      TrendsChartCombined(key: UniqueKey()),
      SummaryTable(key: UniqueKey()),
      DevicesPage(),
      ProfileDrawer(),
    ];
    // Map<String, Widget> list = {
    //   'trends': TrendsChartCombined(key: UniqueKey()),
    //   'billing': SummaryTable(key: UniqueKey()),
    //   'activity': DevicesPage(),
    //   'profile': ProfileDrawer(),
    //   //trends and summary
    //   // ReadCombined(),
    //   // WriteTab(),
    //   // CalibratePage(),
    //   // ActionsPage()
    // };
    return WillPopScope(
      onWillPop: () {
        return Future.value(false);
      },
      child: Scaffold(
        bottomNavigationBar: BottomAppBar(
          key: UniqueKey(),
          height: 67.h,
          padding: const EdgeInsets.all(0),
          color:
              Provider.of<ThemeNotifier>(context).currentTheme.bottomNavColor,
          child: BlocBuilder<DashboardBloc, DashboardState>(
            buildWhen: (previous, current) => current is ChangeDashBoardNav,
            builder: (context, state) {
              int currentPositionOfBottomNav =
                  BlocProvider.of<DashboardBloc>(context).bottomNavPos;
              // if (MainDashboardPage.bottomNavTabs.length == 3) {
              //   bottomNavTabIcons = [
              //     'trends',
              //     'billing',
              //     'profile',
              //   ];
              //   list = [
              //     TrendsChartCombined(key: UniqueKey()),
              //     SummaryTable(key: UniqueKey()),
              //     ProfileDrawer(),
              //   ];
              // } else {
              //   List<String> bottomNavTabIcons = [
              //     'trends',
              //     'billing',
              //     'activity',
              //     'profile',
              //   ];
              //   list = [
              //     TrendsChartCombined(key: UniqueKey()),
              //     SummaryTable(key: UniqueKey()),
              //     DevicesPage(),
              //     ProfileDrawer(),
              //   ];
              // } //TODO: Implement properly
              return Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                  MainDashboardPage.bottomNavTabs.length,
                  (index) => GestureDetector(
                    child: Container(
                      color: Provider.of<ThemeNotifier>(context)
                          .currentTheme
                          .bottomNavColor,
                      width: width / MainDashboardPage.bottomNavTabs.length,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Icon(
                          //   bottomNavTabsIcons[index],
                          //   color: currentPositionOfBottomNav == index
                          //       ? selectedColor[index]
                          //       : Provider.of<ThemeNotifier>(context)
                          //           .currentTheme
                          //           .inactiveBottomNavbarIconColor,
                          //   size: 45.minSp,
                          //   fill: 0.0,
                          // ),
                          SvgPicture.asset(
                            "assets/icons/${bottomNavTabIcons[index]}.svg",
                            color: currentPositionOfBottomNav == index
                                ? selectedColor[index]
                                : Provider.of<ThemeNotifier>(context)
                                    .currentTheme
                                    .inactiveBottomNavbarIconColor,
                            width: 45.minSp,
                            height: 45.minSp,
                          ),
                          Text(
                            MainDashboardPage.bottomNavTabs[index]
                                .toUpperCase(),
                            style: GoogleFonts.robotoMono(
                              color: currentPositionOfBottomNav == index
                                  ? selectedColor[index]
                                  : Provider.of<ThemeNotifier>(context)
                                      .currentTheme
                                      .inactiveBottomNavbarIconColor,
                              fontSize: 16.minSp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    onTap: () {
                      BlocProvider.of<DashboardBloc>(context)
                          .switchBottomNavPos(index);
                    },
                  ),
                ),
              );
            },
          ),
        ),
        drawerEnableOpenDragGesture: false,
        key: _scaffoldKey,
        backgroundColor:
            Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
        drawerEdgeDragWidth: 0.0,
        resizeToAvoidBottomInset: true,
        // endDrawer: DrawerWithAlert(
        //   drawerIndex: drawerIndex,
        //   drawerName: drawerName,
        // ),
        appBar: CustomAppBar(choiceAction: null),
        body: Row(
          children: [
            LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
              return SizedBox(
                height: constraints.maxHeight,
                width: width,
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                      height: constraints.maxHeight,
                      // child: a[currentPositionOfLeftCarousel]
                      //     [currentPositionOfCarousel],
                      child: BlocBuilder<DashboardBloc, DashboardState>(
                          buildWhen: (previous, current) =>
                              current is ChangeDashBoardNav,
                          builder: (context, state) {
                            int bottomNavPos =
                                BlocProvider.of<DashboardBloc>(context)
                                    .bottomNavPos;
                            return IndexedStack(
                              index: bottomNavPos,
                              children:
                                  list, //MainDashboardPage.bottomNavTabs.map((tab) => list[tab] ?? SizedBox.shrink()).toList(),
                            );
                          }),
                    ),
                  ],
                ),
              );
            })
          ],
        ),
      ),
    );
  }
}
