import 'package:equatable/equatable.dart';

/// Domain entity representing an authenticated user
/// 
/// This entity encapsulates all user-related information in the authentication domain.
/// It is immutable and follows clean architecture principles by having no external dependencies.
class AuthUser extends Equatable {
  /// Unique identifier for the user
  final String id;
  
  /// User's full name
  final String name;
  
  /// User's email address
  final String email;
  
  /// Whether the user's email has been verified
  final bool emailVerified;
  
  /// User's phone number
  final String phone;
  
  /// Whether the user's phone number has been verified
  final bool phoneVerified;
  
  /// Timestamp of the last password change (in milliseconds since epoch)
  final String? lastPassChange;
  
  /// Timestamp of the last profile update (in milliseconds since epoch)
  final int? lastUpdate;
  
  /// Multi-factor authentication status ('0', 'app', 'sms')
  final String? multiFactor;

  const AuthUser({
    required this.id,
    required this.name,
    required this.email,
    required this.emailVerified,
    required this.phone,
    required this.phoneVerified,
    this.lastPassChange,
    this.lastUpdate,
    this.multiFactor,
  });

  /// Creates an AuthUser instance from a JSON map
  factory AuthUser.fromJson(Map<String, dynamic> json) {
    return AuthUser(
      id: json['userID'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      emailVerified: json['emailVerified'] ?? false,
      phone: json['phone'] ?? '',
      phoneVerified: json['phoneVerified'] ?? false,
      lastPassChange: json['lastPassChange'],
      lastUpdate: json['lastUpdate'],
      multiFactor: json['multiFactor'],
    );
  }

  /// Converts the AuthUser instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userID': id, // For backward compatibility
      'name': name,
      'email': email,
      'emailVerified': emailVerified,
      'phone': phone,
      'phoneVerified': phoneVerified,
      if (lastPassChange != null) 'lastPassChange': lastPassChange,
      if (lastUpdate != null) 'lastUpdate': lastUpdate,
      if (multiFactor != null) 'multiFactor': multiFactor,
    };
  }

  /// Creates a copy of this AuthUser with the given fields replaced with new values
  AuthUser copyWith({
    String? id,
    String? name,
    String? email,
    bool? emailVerified,
    String? phone,
    bool? phoneVerified,
    String? lastPassChange,
    int? lastUpdate,
    String? multiFactor,
  }) {
    return AuthUser(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerified: emailVerified ?? this.emailVerified,
      phone: phone ?? this.phone,
      phoneVerified: phoneVerified ?? this.phoneVerified,
      lastPassChange: lastPassChange ?? this.lastPassChange,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      multiFactor: multiFactor ?? this.multiFactor,
    );
  }

  /// Returns whether the user has two-factor authentication enabled
  bool get hasTwoFactorEnabled {
    return multiFactor == 'app' || multiFactor == 'sms';
  }

  /// Returns whether both email and phone are verified
  bool get isFullyVerified {
    return emailVerified && phoneVerified;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        emailVerified,
        phone,
        phoneVerified,
        lastPassChange,
        lastUpdate,
        multiFactor,
      ];

  @override
  String toString() {
    return 'AuthUser(id: $id, name: $name, email: $email, emailVerified: $emailVerified, '
        'phone: $phone, phoneVerified: $phoneVerified, hasTwoFactor: $hasTwoFactorEnabled)';
  }
}
