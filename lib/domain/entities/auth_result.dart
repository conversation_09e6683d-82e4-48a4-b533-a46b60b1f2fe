import 'package:equatable/equatable.dart';
import 'auth_user.dart';

/// Domain entity representing the result of an authentication operation
/// 
/// This entity encapsulates the outcome of authentication attempts including
/// login, two-factor verification, and token refresh operations.
/// It is immutable and follows clean architecture principles.
class AuthResult extends Equatable {
  /// Whether the authentication operation was successful
  final bool success;
  
  /// The authenticated user (if authentication was successful)
  final AuthUser? user;
  
  /// Two-factor authentication reference code (if 2FA is required)
  final String? twoFactorRefCode;
  
  /// Error message (if authentication failed)
  final String? error;
  
  /// Whether two-factor authentication is required
  final bool requiresTwoFactor;
  
  /// Access token (if authentication was successful)
  final String? accessToken;
  
  /// Refresh token (if authentication was successful)
  final String? refreshToken;

  const AuthResult({
    required this.success,
    this.user,
    this.twoFactorRefCode,
    this.error,
    this.requiresTwoFactor = false,
    this.accessToken,
    this.refreshToken,
  });

  /// Creates a successful authentication result
  factory AuthResult.success({
    required AuthUser user,
    String? accessToken,
    String? refreshToken,
  }) {
    return AuthResult(
      success: true,
      user: user,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }

  /// Creates a result indicating two-factor authentication is required
  factory AuthResult.twoFactorRequired({
    required String twoFactorRefCode,
  }) {
    return AuthResult(
      success: false,
      requiresTwoFactor: true,
      twoFactorRefCode: twoFactorRefCode,
    );
  }

  /// Creates a failed authentication result
  factory AuthResult.failure({
    required String error,
  }) {
    return AuthResult(
      success: false,
      error: error,
    );
  }

  /// Creates an AuthResult instance from a JSON map
  factory AuthResult.fromJson(Map<String, dynamic> json) {
    return AuthResult(
      success: json['success'] ?? false,
      user: json['user'] != null ? AuthUser.fromJson(json['user']) : null,
      twoFactorRefCode: json['twoFactorRefCode'],
      error: json['error'],
      requiresTwoFactor: json['requiresTwoFactor'] ?? false,
      accessToken: json['accessToken'],
      refreshToken: json['refreshToken'],
    );
  }

  /// Converts the AuthResult instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      if (user != null) 'user': user!.toJson(),
      if (twoFactorRefCode != null) 'twoFactorRefCode': twoFactorRefCode,
      if (error != null) 'error': error,
      'requiresTwoFactor': requiresTwoFactor,
      if (accessToken != null) 'accessToken': accessToken,
      if (refreshToken != null) 'refreshToken': refreshToken,
    };
  }

  /// Creates a copy of this AuthResult with the given fields replaced with new values
  AuthResult copyWith({
    bool? success,
    AuthUser? user,
    String? twoFactorRefCode,
    String? error,
    bool? requiresTwoFactor,
    String? accessToken,
    String? refreshToken,
  }) {
    return AuthResult(
      success: success ?? this.success,
      user: user ?? this.user,
      twoFactorRefCode: twoFactorRefCode ?? this.twoFactorRefCode,
      error: error ?? this.error,
      requiresTwoFactor: requiresTwoFactor ?? this.requiresTwoFactor,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
    );
  }

  /// Returns whether the result has authentication tokens
  bool get hasTokens {
    return accessToken != null && refreshToken != null;
  }

  /// Returns whether the authentication is complete (successful and has tokens)
  bool get isComplete {
    return success && hasTokens && user != null;
  }

  /// Returns whether the result indicates a failure
  bool get isFailure {
    return !success && !requiresTwoFactor;
  }

  @override
  List<Object?> get props => [
        success,
        user,
        twoFactorRefCode,
        error,
        requiresTwoFactor,
        accessToken,
        refreshToken,
      ];

  @override
  String toString() {
    if (success) {
      return 'AuthResult.success(user: ${user?.email}, hasTokens: $hasTokens)';
    } else if (requiresTwoFactor) {
      return 'AuthResult.twoFactorRequired(refCode: $twoFactorRefCode)';
    } else {
      return 'AuthResult.failure(error: $error)';
    }
  }
}
