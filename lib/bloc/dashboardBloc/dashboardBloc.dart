import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:water_metering/main.dart';
import 'package:water_metering/utils/alert_message.dart';
import 'package:water_metering/utils/excel_helpers.dart';
import 'package:water_metering/view_model/custom_exception.dart';
import 'package:water_metering/view_model/data_post_requests.dart';
import 'package:water_metering/views/pages/DashboardPage2.dart';

import '../../model/chartModels.dart';
import '../../model/filterAndSummaryForProject.dart';
import '../../model/userInfo.dart';
import '../../domain/entities/auth_user.dart';
import '../auth/auth.dart';

import '../../view_model/loginPostRequests.dart';
import 'dashboardEvent.dart';
import 'dashboardState.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  static ChangeNotifier toUpdateProfile = ChangeNotifier();
  final AuthBloc _authBloc;
  late StreamSubscription<AuthState> _authSubscription;

  UserInfo userInfo = const UserInfo(
    id: "1234",
    name: "Fake name",
    email: "<EMAIL>",
    emailVerified: false,
    phone: "+919999999999",
    phoneVerified: false,
  );

  List<String> projects = [];
  List<Session> sessions = [];

  FilterAndSummaryForProject? filterData;
  var summaryData;
  var devicesData;
  var allDevices;
  NudronChartMap? nudronChartData;
  int screenIndex = 0;
  GlobalKey repaintBoundaryKey = GlobalKey(); // Define a global key
  changeKey(GlobalKey key) {
    repaintBoundaryKey = key;
  }

  // Future<void> captureSS(BuildContext context) async {
  //   try {
  //     // Show loading indicator
  //     showDialog(
  //       context: context,
  //       barrierDismissible: false,
  //       builder: (BuildContext context) {
  //         return Center(
  //           child: Container(
  //             padding: EdgeInsets.all(16),
  //             decoration: BoxDecoration(
  //               color:
  //                   Provider.of<ThemeNotifier>(context).currentTheme.dialogBG,
  //               borderRadius: BorderRadius.circular(8),
  //             ),
  //             child: Column(
  //               mainAxisSize: MainAxisSize.min,
  //               children: [
  //                 CircularProgressIndicator(
  //                   valueColor: AlwaysStoppedAnimation<Color>(
  //                     Provider.of<ThemeNotifier>(context)
  //                         .currentTheme
  //                         .loginTitleColor,
  //                   ),
  //                 ),
  //                 SizedBox(height: 16),
  //                 Text(
  //                   "Preparing chart...",
  //                   style: TextStyle(
  //                     color: Provider.of<ThemeNotifier>(context)
  //                         .currentTheme
  //                         .basicAdvanceTextColor,
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         );
  //       },
  //     );
  //
  //     final screenshotController = ScreenshotController();
  //
  //     // Get necessary providers and data
  //     final mediaQuery = MediaQuery.of(context);
  //     final themeNotifier = Provider.of<ThemeNotifier>(context, listen: false);
  //     final dashboardBloc = BlocProvider.of<DashboardBloc>(context);
  //
  //     // Create widget with proper dimensions and rotation
  //     final screenWidth = mediaQuery.size.height;
  //     final screenHeight = mediaQuery.size.width;
  //
  //     final widgetToCapture = ScreenUtilInit(
  //       designSize: Size(screenWidth, screenHeight),
  //       minTextAdapt: true,
  //       splitScreenMode: true,
  //       builder: (_, child) => MultiBlocProvider(
  //         providers: [
  //           BlocProvider.value(value: dashboardBloc),
  //         ],
  //         child: MultiProvider(
  //           providers: [
  //             ChangeNotifierProvider.value(value: themeNotifier),
  //           ],
  //           child: MaterialApp(
  //             debugShowCheckedModeBanner: false,
  //             builder: (context, child) => MediaQuery(
  //               data: mediaQuery.copyWith(
  //                 size: Size(screenWidth, screenHeight),
  //                 devicePixelRatio: mediaQuery.devicePixelRatio,
  //               ),
  //               child: Scaffold(
  //                 backgroundColor: themeNotifier.currentTheme.bgColor,
  //                 body: BackgroundChart(),
  //               ),
  //             ),
  //           ),
  //         ),
  //       ),
  //     );
  //
  //     // Capture with a delay to ensure proper rendering
  //     final capturedImage = await screenshotController.captureFromWidget(
  //       widgetToCapture,
  //       delay: const Duration(milliseconds: 300),
  //       targetSize: Size(screenWidth, screenHeight),
  //       context: context,
  //       pixelRatio: mediaQuery.devicePixelRatio,
  //     );
  //
  //     // Remove loading dialog
  //     if (context.mounted) {
  //       Navigator.of(context).pop();
  //     }
  //
  //     // Request storage permission
  //     final permissionStatus = await Permission.storage.request();
  //     if (!permissionStatus.isGranted) {
  //       if (context.mounted) {
  //         CustomAlert.showCustomScaffoldMessenger(
  //           context,
  //           "Storage permission required to save screenshot",
  //           AlertType.error,
  //         );
  //       }
  //       return;
  //     }
  //
  //     // Get appropriate directory
  //     Directory? directory;
  //     if (Platform.isAndroid) {
  //       directory = await getExternalStorageDirectory();
  //     } else if (Platform.isIOS) {
  //       directory = await getApplicationDocumentsDirectory();
  //     } else {
  //       throw UnsupportedError("Unsupported platform");
  //     }
  //
  //     if (directory == null) {
  //       throw Exception("Error: External storage directory not available");
  //     }
  //
  //     // Generate unique filename with timestamp and date
  //     final now = DateTime.now();
  //     final formattedDate =
  //         "${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}";
  //     final timestamp = now.millisecondsSinceEpoch;
  //     final filePath =
  //         "${directory.path}/chart_${formattedDate}_$timestamp.png";
  //
  //     // Save the file
  //     final file = File(filePath);
  //     await file.create(recursive: true);
  //     await file.writeAsBytes(capturedImage);
  //
  //     if (context.mounted) {
  //       // Show success message
  //       CustomAlert.showCustomScaffoldMessenger(
  //         context,
  //         "Chart saved successfully",
  //         AlertType.success,
  //       );
  //
  //       // Open the saved file
  //       await OpenFile.open(filePath, linuxByProcess: true);
  //     }
  //   } catch (e) {
  //     // Remove loading dialog if still showing
  //     if (context.mounted && Navigator.of(context).canPop()) {
  //       Navigator.of(context).pop();
  //     }
  //
  //     if (context.mounted) {
  //       CustomAlert.showCustomScaffoldMessenger(
  //         context,
  //         "Error saving chart: ${e.toString()}",
  //         AlertType.error,
  //       );
  //     }
  //   }
  // }

  // Future<void> captureSS() async {
  //   try {
  //     ScreenshotController screenshotController = ScreenshotController();
  //
  //     screenshotController
  //         .captureFromWidget(BackgroundChart())
  //         .then((capturedImage) async {
  //       await Permission.storage.request();
  //
  //       Directory? directory;
  //       if (Platform.isAndroid) {
  //         directory = await getExternalStorageDirectory();
  //       } else if (Platform.isIOS) {
  //         directory = await getApplicationDocumentsDirectory();
  //       } else {
  //         String errorMsg = "Unsupported platform";
  //         CustomAlert.showCustomScaffoldMessenger(
  //             mainNavigatorKey.currentContext!, errorMsg, AlertType.error);
  //         throw UnsupportedError(errorMsg);
  //       }
  //
  //       if (directory == null) {
  //         throw Exception("Error: External storage directory not available");
  //       }
  //
  //       String filePath = "${directory.path}/chart.png";
  //       File file = File(filePath);
  //       await file.create(recursive: true);
  //       await file.writeAsBytes(capturedImage);
  //
  //       // Open the saved file
  //       OpenFile.open(filePath, linuxByProcess: true);
  //     });
  //   } catch (e) {
  //     CustomAlert.showCustomScaffoldMessenger(
  //       mainNavigatorKey.currentContext!,
  //       "Error in capturing screenshot: ${e.toString()}",
  //       AlertType.error,
  //     );
  //   }
  // }

  Future<void> captureSS() async {
    if (Platform.isAndroid) {
      try {
        if (screenIndex == 0) {
          screenIndex = 1;
          emit(ChangeScreen());
        }

        WidgetsBinding.instance.addPostFrameCallback((_) async {
          RenderRepaintBoundary boundary = repaintBoundaryKey.currentContext!
              .findRenderObject() as RenderRepaintBoundary;

          ui.Image image = await boundary.toImage(pixelRatio: 3);
          screenIndex = 0;
          emit(ChangeScreen());
          ByteData? byteData =
              await image.toByteData(format: ui.ImageByteFormat.png);

          if (byteData == null) {
            throw Exception("Error in capturing screenshot: ByteData is null");
          }

          Uint8List pngBytes = byteData.buffer.asUint8List();

          // Request storage permission
          await Permission.storage.request();

          Directory? directory = await getExternalStorageDirectory();
          if (directory == null) {
            throw Exception("Error: External storage directory not available");
          }

          String filePath = "${directory.path}/chart.png";
          File file = File(filePath);
          await file.create(recursive: true);
          await file.writeAsBytes(pngBytes);

          // Open the saved file
          OpenFile.open(filePath, linuxByProcess: true);
        });
      } catch (e) {
        CustomAlert.showCustomScaffoldMessenger(
          mainNavigatorKey.currentContext!,
          "Error in capturing screenshot: ${e.toString()}",
          AlertType.error,
        );
      }
    } else if (Platform.isIOS) {
      try {
        if (screenIndex == 0) {
          screenIndex = 1;
          emit(ChangeScreen());
        }

        // Allow some time for the UI to render fully
        // await Future.delayed(Duration(milliseconds: 50));

        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // await Future.delayed(Duration(milliseconds: 20000));
          RenderRepaintBoundary boundary = repaintBoundaryKey.currentContext!
              .findRenderObject() as RenderRepaintBoundary;

          // Try to capture the screenshot

          ui.Image image = await boundary.toImage(pixelRatio: 3);
          screenIndex = 0;
          emit(ChangeScreen());
          ByteData? byteData =
              await image.toByteData(format: ui.ImageByteFormat.png);

          if (byteData == null) {
            throw Exception("Error in capturing screenshot: ByteData is null");
          }

          Uint8List pngBytes = byteData.buffer.asUint8List();

          // Request storage permission
          await Permission.storage.request();

          Directory? directory = await getApplicationDocumentsDirectory();

          String filePath = "${directory.path}/chart.png";
          File file = File(filePath);
          await file.create(recursive: true);
          await file.writeAsBytes(pngBytes);

          // Open the saved file
          OpenFile.open(filePath, linuxByProcess: true);
        });
      } catch (e) {
        final errorMsg = "Error in capturing screenshot: ${e.toString()}";
        print(errorMsg);
        CustomAlert.showCustomScaffoldMessenger(
            mainNavigatorKey.currentContext!, errorMsg, AlertType.error);
      }
    } else {
      throw UnsupportedError("Unsupported platform");
    }
  }

  changeScreen() {
    screenIndex = 1 - screenIndex;
    emit(ChangeScreen());
  }

  int selectedMonth = getCurrentMonth() - 1;

  List<String> currentFilters = [];

  refreshSummaryPage() {
    print("OOPS I WAS HIT 1");
    if (state is RefreshSummaryPage) {
      emit(RefreshSummaryPage2());
    } else {
      emit(RefreshSummaryPage());
    }
  }

  refreshDevicesPage() {
    print("OOPS I WAS HIT 2");
    if (state is RefreshDevicesPage) {
      emit(RefreshDevicesPage2());
    } else {
      emit(RefreshDevicesPage());
    }
  }

  selectMonth(int month) async {
    String? project = currentFilters.firstOrNull;
    if (project == null) {
      throw CustomException(
          "Please select a project in the TRENDS page first.");
    }

    summaryData = await DataPostRequests.getBillingData(
        project: project, monthNumber: month);
    selectedMonth = month;
    refreshSummaryPage();
  }

  getDevicesData() async {
    String? project = currentFilters.firstOrNull;
    if (project == null) {
      throw CustomException(
          "Please select a project in the TRENDS page first.");
    }

    var response = await DataPostRequests.getFilters(project: project);

    if (response.length > 2 &&
        response[2] is List &&
        response[2].length > 1 &&
        response[2][0] == "Activity") {
      var activityData = response[2][1];

      if (activityData is List) {
        devicesData = activityData;
        allDevices = activityData;
      } else {
        throw CustomException("Unexpected data format in Activity section.");
      }
    } else {
      throw CustomException(
          "Unexpected response format or missing Activity data.");
    }
    print("***************************");
    print(devicesData);
    print("____________________________");
    print(allDevices);
    refreshDevicesPage();
  }

  filterDevices(String query) {
    print("Query is : " + query);

    List header = allDevices[0]; // Save the header
    List dataToFilter = allDevices[1]; // Get the data array to filter

    List filteredData = dataToFilter.where((device) {
      if (device is! List || device.length < 2) {
        print("Invalid");
        return false;
      }

      String id = device[0].toString().toLowerCase(); // ID is at index 0
      String label = device[1].toString().toLowerCase(); // Label is at index 1
      return id.contains(query.toLowerCase()) ||
          label.contains(query.toLowerCase());
    }).toList();

    devicesData = [header, filteredData];

    print(devicesData);
    refreshDevicesPage();
  }

  setBillingFormula(String formula) async {
    String? project = currentFilters.firstOrNull;
    if (project == null) {
      throw CustomException(
          "Please select a project in the TRENDS page first.");
    }

    await DataPostRequests.setBillingFormula(
        project: project, formulaString: formula);
    if (filterData != null) {
      filterData!.summaryFormattedtext = formula;
    }
    refreshSummaryPage();
  }

  static getCurrentMonth() {
    DateTime currentDate = DateTime.now();
    return (currentDate.year - 2020) * 12 + currentDate.month - 1;
  }

  List<String> getMonthNumbers() {
    int currentMonthNumber =
        getCurrentMonth(); // (currentDate.year - startDate.year) * 12 + currentDate.month;

    // Generate the last 12 months
    List<int> months = [];
    for (int i = 1; i < 13; i++) {
      months.add(currentMonthNumber - i);
    }
    return months.map((e) => e.toString()).toList();
  }

  String convertMonthNumberToText(String monthNumber) {
    // Try to parse the string to an integer
    int? monthNum = int.tryParse(monthNumber);

    // If parsing fails, return the original text
    if (monthNum == null) {
      return monthNumber;
    }

    // Calculate the year and month based on the parsed month number
    int year = 2020 + (monthNum) ~/ 12;
    int month = (monthNum) % 12;

    // Define month names
    List<String> monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December"
    ];

    // Convert to "Month-Year" format
    return "${monthNames[month]} $year";
  }

  getFiltersAndSummaryForProject(String project) async {
    return FilterAndSummaryForProject(
        data: await DataPostRequests.getFilters(project: project));
  }

  exportDataToExcel(
    var data,
    bool exportToIncludeWholeData,
    String exportType,
    BuildContext context,
  ) async {
    // try {
    //   await DataPostRequests.exportDataToExcel(
    //       data: trendsData, fileName: "TrendsData");
    // } catch (e) {
    //   CustomAlert.showCustomScaffoldMessenger(mainNavigatorKey.currentContext!,
    //       "Error in exporting data : ${e.toString()}", AlertType.error);
    // }
    CustomAlert.showCustomScaffoldMessenger(mainNavigatorKey.currentContext!,
        "Preparing data for export...", AlertType.info);
    String? path;
    if (exportToIncludeWholeData) {
      path = await ExcelHelper.exportToExcel(
        [
          ["Selected Data", data],
          ["Trends Data", nudronChartData?.getWholeData()],
        ],
        exportType,
        context,
      );
    } else {
      path = await ExcelHelper.exportToExcel(
        [
          ["Selected Data", data]
        ],
        exportType,
        context,
      );
    }

    if (path != null) {
      print("Data exported successfully to $path");
      // CustomAlert.showCustomScaffoldMessenger(mainNavigatorKey.currentContext!,
      //     "Data exported successfully to $path", AlertType.success);
    }
  }

  updateSelectedFilters(
      List<String?> filters, FilterAndSummaryForProject? filterData) async {
    bool toRefreshSummaryPage = false;
    if (currentFilters.firstOrNull != filters.firstOrNull) {
      toRefreshSummaryPage = true;
    }

    currentFilters.clear();
    for (var filter in filters) {
      if (filter != null) {
        currentFilters.add(filter);
      }
    }
    this.filterData = filterData;
    try {
      await updateBottomNavTabs(project: currentFilters.first);
      print(MainDashboardPage.bottomNavTabs);
      emit(ChangeDashBoardNav());
    } catch (e) {
      CustomAlert.showCustomScaffoldMessenger(mainNavigatorKey.currentContext!,
          "Error in loading trends data : ${e.toString()}", AlertType.error);
      return;
    }
    await loadTrendsData(currentFilters.firstOrNull);
    try {
      await loadTrendsData(currentFilters.firstOrNull);
    } catch (e) {
      CustomAlert.showCustomScaffoldMessenger(mainNavigatorKey.currentContext!,
          "Error in loading trends data : ${e.toString()}", AlertType.error);
      return;
    }

    try {
      if (toRefreshSummaryPage) {
        await selectMonth(selectedMonth);
      }
    } catch (e) {
      CustomAlert.showCustomScaffoldMessenger(mainNavigatorKey.currentContext!,
          "Error in loading summary data : ${e.toString()}", AlertType.error);
      return;
    }

    try {
      if (toRefreshSummaryPage) {
        await getDevicesData();
      }
    } catch (e) {
      CustomAlert.showCustomScaffoldMessenger(mainNavigatorKey.currentContext!,
          "Error in loading device data : ${e.toString()}", AlertType.error);
      return;
    }
    refreshSummaryPage();
    refreshDashboard();
  }

  static Future<List<String>?> updateBottomNavTabs(
      {required String? project}) async {
    if (project == null) return ['trends', 'billing', 'activity', 'profile'];
    try {
      // Fetch the response from API
      dynamic response = await DataPostRequests.getFilters(project: project);
      List<String> bottomNavTabs = [];
      // Ensure the response is a list
      if (response is List) {
        // Extract the first element (title) from each top-level list
        bottomNavTabs = response
            .map((item) {
              if (item is List && item.isNotEmpty && item[0] is String) {
                return item[0].toLowerCase(); // Convert to lowercase
              }
              return null;
            })
            .whereType<String>()
            .toList(); // Remove null values
      }
      bottomNavTabs.add('profile');
      print("Updated bottomNavTabs: $bottomNavTabs");
      MainDashboardPage.bottomNavTabs = bottomNavTabs;
      return bottomNavTabs; // Debug print
    } catch (e) {
      print("Error fetching bottomNavTabs: $e");
    }
    return null;
  }

  getDateFromDayNumber(int dayNumber) {
    // var date = DateTime.now().subtract(Duration(days: dayNumber));
    // return "${date.year}-${date.month}-${date.day}";
    //day number is day from 1/1/2020
    var date = DateTime.utc(2020, 1, 1).add(Duration(days: dayNumber));
    //DATE FORMAT: 01-01-2020
    return DateFormat('dd-MMM-yy').format(date);
  }

  updateTrendsData(var newData) {
    // nudronChartData = NudronChartMap((newData[1] == null ||
    //         (newData[1].runtimeType is String && newData[1] == "null") ||
    //         (newData[1].runtimeType is List && newData[1].isEmpty))
    //     ? []
    //     : trendsData[1]
    //         .map<List<dynamic>>((item) => [item[0], item[1].toInt(), item[2]])
    //         .toList());

    nudronChartData = NudronChartMap(newData);
  }

  loadTrendsData(String? project) async {
    if (project == null) {
      return;
    }

    updateTrendsData(await DataPostRequests.getChartData(
        project: project,
        selectedLevels:
            currentFilters.length > 1 ? currentFilters.sublist(1) : [""]));
  }

  selectProject(int selectedIndex) async {
    if (selectedIndex < projects.length && selectedIndex >= 0) {
      if (projects[selectedIndex] == currentFilters.firstOrNull) {
        return filterData;
      }
      return await getFiltersAndSummaryForProject(projects[selectedIndex]);
    }
    return null;
  }

  checkAndAddProject(String projectName) {
    if (projectName.endsWith("Water Metering")) {
      projects.add(projectName.substring(0, projectName.length - 15));
    }
  }

  /// Initialize user info from authenticated user
  /// This method now gets user info from AuthBloc instead of making direct API calls
  initUserInfo() async {
    try {
      // Get the current authenticated user from AuthBloc
      final currentUser = _authBloc.currentUser;
      if (currentUser == null) {
        throw ("No authenticated user found");
      }

      // Convert AuthUser to UserInfo for backward compatibility
      userInfo = UserInfo(
        id: currentUser.id,
        name: currentUser.name,
        email: currentUser.email,
        emailVerified: currentUser.emailVerified,
        phone: currentUser.phone,
        phoneVerified: currentUser.phoneVerified,
      );

      // Still need to get projects and sessions from token check
      // This will be refactored in later tasks to use proper repositories
      Map<dynamic, dynamic> json = await LoginPostRequests.tokenCheck();
      this.projects.clear();
      this.sessions.clear();

      var projects = json["projects"];
      for (var project in projects) {
        if (project["project"] != null) {
          checkAndAddProject(project["project"]);
        }
      }

      var sessions = json["profile"]?["sessions"];
      if (sessions != null) {
        for (var session in sessions) {
          this.sessions.add(Session.fromJson(session));
        }
      }

      // Update two-factor authentication status
      String mfa = currentUser.multiFactor ?? '0';
      await LoginPostRequests.twoFAToggleVal(mfa == 'app' || mfa == 'sms');
    } catch (e) {
      throw ("Error in getting user info $e");
    }
  }

  updateProfile() async {
    await initUserInfo();
    if (state is UserInfoUpdate) {
      emit(UserInfoUpdate2());
    } else {
      emit(UserInfoUpdate());
    }
  }

  switchBottomNavPos(int index) {
    if (bottomNavPos == index) {
      return;
    }
    bottomNavPos = index;
    emit(ChangeDashBoardNav());
  }

  int bottomNavPos = 0;

  refreshDashboard() {
    print("OOPS I WAS HIT 3");
    if (state is RefreshDashboard) {
      emit(RefreshDashboard2());
    } else {
      emit(RefreshDashboard());
    }
  }

  loadInitialData() async {
    try {
      // Only load data if user is authenticated
      if (!_authBloc.isAuthenticated) {
        emit(DashboardPageError(message: "User not authenticated"));
        return;
      }

      await initUserInfo();
      LoginPostRequests.refreshListeners();
      emit(DashboardPageLoaded());
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      emit(DashboardPageError(message: e.toString()));
    }
  }

  DashboardBloc({required AuthBloc authBloc})
      : _authBloc = authBloc,
        super(DashboardPageInitial()) {
    toUpdateProfile.addListener(updateProfile);

    // Listen to AuthBloc state changes
    _authSubscription = _authBloc.stream.listen((authState) {
      if (authState is AuthAuthenticated) {
        // User is authenticated, load dashboard data
        loadInitialData();
      } else if (authState is AuthUnauthenticated) {
        // User is not authenticated, clear data
        _clearUserData();
      }
    });

    // Check initial auth state
    if (_authBloc.isAuthenticated) {
      loadInitialData();
    }
  }

  /// Clear user data when user is unauthenticated
  void _clearUserData() {
    projects.clear();
    sessions.clear();
    userInfo = const UserInfo(
      id: "",
      name: "",
      email: "",
      emailVerified: false,
      phone: "",
      phoneVerified: false,
    );
  }

  @override
  Future<void> close() {
    _authSubscription.cancel();
    toUpdateProfile.removeListener(updateProfile);
    return super.close();
  }
}
